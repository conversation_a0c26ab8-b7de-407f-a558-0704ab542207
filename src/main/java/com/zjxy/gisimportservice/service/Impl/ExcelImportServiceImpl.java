package com.zjxy.gisimportservice.service.Impl;

import com.alibaba.excel.EasyExcel;
import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.gisimportservice.config.ExcelImportConfig;
import com.zjxy.gisimportservice.entity.*;
import com.zjxy.gisimportservice.listener.ExcelDataListener;
import com.zjxy.gisimportservice.monitor.ExcelImportMetrics;
import com.zjxy.gisimportservice.service.*;
import com.zjxy.gisimportservice.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Excel导入服务实现类
 *
 * 实现Excel文件的导入、解析、验证和数据处理功能
 * 集成现有的gisimportservice架构和服务
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-28
 */
@Slf4j
@Service
public class ExcelImportServiceImpl implements ExcelImportService {

    @Autowired
    private GisManageTemplateService templateService;

    @Autowired
    private DataValidationService validationService;

    @Autowired
    private CoordinateTransformService coordinateTransformService;

    @Autowired
    private HighPerformanceBatchInsertService batchInsertService;

    @Autowired
    private GisImportTaskService taskService;

    @Autowired
    private ExcelImportConfig excelConfig;

    @Autowired
    private ExcelImportMetrics metrics;

    @Value("${gis.import.task.upload.temp-path:/temp/gis-uploads}")
    private String tempPath;

    @Override
    public ExcelImportResult importExcelData(MultipartFile file, Integer templateId,
                                           String sheetName, String target, String createdBy)
                                           throws IOException, ParseException {

        log.info("开始Excel导入 - 文件: {}, 模板ID: {}, 工作表: {}, 目标: {}",
                file.getOriginalFilename(), templateId, sheetName, target);

        LocalDateTime startTime = LocalDateTime.now();
        ExcelImportResult.ExcelImportResultBuilder resultBuilder = ExcelImportResult.builder()
                .startTime(startTime)
                .success(false);

        String taskId = "excel_import_" + System.currentTimeMillis();

        try {
            // 1. 参数验证
            validateImportParameters(file, templateId);

            // 2. 获取模板配置
            GisManageTemplate template = getAndValidateTemplate(templateId);

            // 3. 记录导入开始
            metrics.recordImportStart(taskId, file.getOriginalFilename(), file.getSize(), template.getId().toString());

            // 4. 创建导入任务
            GisImportTask task = createImportTask(file, template, createdBy);

            // 5. 执行Excel导入
            ExcelImportResult result = executeExcelImport(file, template, sheetName, target, task);

            // 6. 更新任务状态
            updateTaskStatus(task, result);

            // 7. 记录导入完成
            metrics.recordImportComplete(taskId, result.isSuccess(), result.getProcessingTimeMs(),
                    result.getTotalRecords(), result.getSuccessRecords(), result.getErrorRecords());

            log.info("Excel导入完成 - 任务ID: {}, 总记录: {}, 成功: {}, 错误: {}",
                    task != null ? task.getId() : "N/A", result.getTotalRecords(), result.getSuccessRecords(), result.getErrorRecords());

            return result;

        } catch (Exception e) {
            log.error("Excel导入失败", e);

            // 记录导入错误
            metrics.recordImportError(taskId, e.getMessage());

            return resultBuilder
                    .success(false)
                    .message("Excel导入失败: " + e.getMessage())
                    .endTime(LocalDateTime.now())
                    .processingTimeMs(calculateProcessingTime(startTime))
                    .build();
        }
    }

    @Override
    public Map<String, Object> analyzeExcelFile(MultipartFile file, Integer headerRow) throws IOException {
        log.info("开始分析Excel文件 - 文件: {}, 表头行: {}", file.getOriginalFilename(), headerRow);

        try {
            // 验证文件
            validateFile(file);

            // 分析Excel结构
            Map<String, Object> analysis = ExcelUtil.analyzeExcelStructure(file, headerRow);

            // 添加额外分析信息
            analysis.put("analysisTime", LocalDateTime.now());
            analysis.put("fileValid", true);

            log.info("Excel文件分析完成 - 工作表数: {}", analysis.get("totalSheets"));
            return analysis;

        } catch (Exception e) {
            log.error("Excel文件分析失败", e);
            throw new IOException("Excel文件分析失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<String> getSheetNames(MultipartFile file) throws IOException {
        log.info("获取Excel工作表名称 - 文件: {}", file.getOriginalFilename());

        try {
            validateFile(file);
            List<String> sheetNames = ExcelUtil.getSheetNames(file);
            log.info("获取到 {} 个工作表", sheetNames.size());
            return sheetNames;

        } catch (Exception e) {
            log.error("获取Excel工作表名称失败", e);
            throw new IOException("获取Excel工作表名称失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, List<Map<String, Object>>> analyzeExcelAttachment(MultipartFile file, Integer templateId)
            throws IOException {
        log.info("分析Excel附件数据 - 文件: {}, 模板ID: {}", file.getOriginalFilename(), templateId);

        try {
            // 获取模板配置
            GisManageTemplate template = templateService.getTemplateById(templateId);
            if (template == null) {
                throw new IllegalArgumentException("模板不存在，模板ID: " + templateId);
            }

            // 分析附件数据
            Map<String, List<Map<String, Object>>> result = new HashMap<>();

            // 这里可以根据模板配置分析附件数据
            // 暂时返回空结果，后续可以扩展

            log.info("Excel附件数据分析完成");
            return result;

        } catch (Exception e) {
            log.error("Excel附件数据分析失败", e);
            throw new IOException("Excel附件数据分析失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> validateExcelData(MultipartFile file, Integer templateId, String sheetName)
            throws IOException {
        log.info("验证Excel数据 - 文件: {}, 模板ID: {}, 工作表: {}",
                file.getOriginalFilename(), templateId, sheetName);

        try {
            // 获取模板配置
            GisManageTemplate template = getAndValidateTemplate(templateId);

            // 执行验证模式的导入
            ExcelImportResult result = executeExcelImport(file, template, sheetName, "valid", null);

            // 转换为验证结果格式
            Map<String, Object> validationResult = new HashMap<>();
            validationResult.put("valid", result.isSuccess());
            validationResult.put("totalRecords", result.getTotalRecords());
            validationResult.put("errorRecords", result.getErrorRecords());
            validationResult.put("errorRate", result.getErrorRate());
            validationResult.put("errors", result.getErrors());
            validationResult.put("warnings", result.getWarnings());

            log.info("Excel数据验证完成 - 总记录: {}, 错误: {}",
                    result.getTotalRecords(), result.getErrorRecords());

            return validationResult;

        } catch (Exception e) {
            log.error("Excel数据验证失败", e);
            throw new IOException("Excel数据验证失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ExcelImportResult batchImportExcelData(MultipartFile file, Integer templateId,
                                                Integer batchSize, String target, String createdBy)
                                                throws IOException, ParseException {
        log.info("批量导入Excel数据 - 文件: {}, 模板ID: {}, 批次大小: {}",
                file.getOriginalFilename(), templateId, batchSize);

        // 获取模板并设置批次大小
        GisManageTemplate template = getAndValidateTemplate(templateId);

        // 设置Excel配置中的批次大小
        Map<String, Object> excelConfigMap = template.getExcelConfig();
        if (excelConfigMap == null) {
            excelConfigMap = new HashMap<>();
        }
        excelConfigMap.put("batchSize", batchSize);
        template.setExcelConfig(excelConfigMap);

        // 执行导入
        return importExcelData(file, templateId, null, target, createdBy);
    }

    /**
     * 执行Excel导入的核心逻辑
     */
    private ExcelImportResult executeExcelImport(MultipartFile file, GisManageTemplate template,
                                               String sheetName, String target, GisImportTask task)
                                               throws IOException {

        LocalDateTime startTime = LocalDateTime.now();

        // 创建结果对象
        ExcelImportResult.ExcelImportResultBuilder resultBuilder = ExcelImportResult.builder()
                .startTime(startTime)
                .taskId(task != null ? task.getId() : null);

        try (InputStream inputStream = file.getInputStream()) {

            // 首先分析Excel文件结构
            analyzeExcelFileStructure(file);

            // 确定工作表名称
            String targetSheetName = sheetName;
            if (targetSheetName == null || targetSheetName.trim().isEmpty()) {
                targetSheetName = template.getSheetName();
                if (targetSheetName == null || targetSheetName.trim().isEmpty()) {
                    // 如果模板中也没有配置工作表名称，使用默认值或读取第一个工作表
                    targetSheetName = null; // EasyExcel会自动读取第一个工作表
                    log.info("未指定工作表名称，将读取第一个工作表");
                }
            }
            log.info("目标工作表名称: '{}'", targetSheetName);

            // 获取批次大小
            int batchSize = template.getExcelBatchSize();
            log.info("批次大小: {}", batchSize);

            // 创建数据监听器
            ExcelDataListener listener = new ExcelDataListener(
                    template,
                    validationService,
                    coordinateTransformService,
                    batchInsertService,
                    batchSize,
                    target
            );

            // 创建一个简单的测试监听器来验证数据读取
            com.alibaba.excel.event.AnalysisEventListener<Map<Integer, Object>> testListener =
                new com.alibaba.excel.event.AnalysisEventListener<Map<Integer, Object>>() {
                    private int rowCount = 0;

                    @Override
                    public void invoke(Map<Integer, Object> data, com.alibaba.excel.context.AnalysisContext context) {
                        rowCount++;
                        log.info("测试监听器 - 读取到第 {} 行数据: {}", rowCount, data);

                        // 同时调用原始监听器
                        listener.invoke(data, context);
                    }

                    @Override
                    public void doAfterAllAnalysed(com.alibaba.excel.context.AnalysisContext context) {
                        log.info("测试监听器 - 数据读取完成，总行数: {}", rowCount);
                        listener.doAfterAllAnalysed(context);
                    }
                };

            // 使用EasyExcel读取数据
            int headRowNumber = template.getExcelDataStartRow();
            log.info("Excel读取配置 - 工作表: '{}', 表头行数: {}, 模板thLine: {}",
                    targetSheetName, headRowNumber, template.getThLine());

            // 打印模板的详细配置
            log.info("模板详细配置:");
            log.info("  模板ID: {}", template.getId());
            log.info("  模板类型: {}", template.getTemplateType());
            log.info("  目标表: {}", template.getTableName());
            log.info("  字段映射数量: {}", template.getMap() != null ? template.getMap().size() : 0);

            // 构建EasyExcel读取器
            if (targetSheetName != null && !targetSheetName.trim().isEmpty()) {
                // 指定工作表名称
                log.info("读取指定工作表: '{}'", targetSheetName);
                EasyExcel.read(inputStream, testListener)
                        .sheet(targetSheetName)
                        .headRowNumber(headRowNumber)
                        .doRead();
            } else {
                // 读取第一个工作表
                log.info("读取第一个工作表（索引0）");
                EasyExcel.read(inputStream, testListener)
                        .sheet(0)
                        .headRowNumber(headRowNumber)
                        .doRead();
            }

            log.info("Excel读取完成 - 监听器统计: 总记录={}, 成功={}, 错误={}",
                    listener.getTotalRecords(), listener.getSuccessRecords(), listener.getErrorRecords());

            // 构建结果
            LocalDateTime endTime = LocalDateTime.now();
            long processingTime = calculateProcessingTime(startTime);

            ExcelImportResult result = resultBuilder
                    .success(!listener.isHasError() || "valid".equals(target))
                    .message(listener.isHasError() ? "导入过程中发现错误" : "导入成功")
                    .totalRecords(listener.getTotalRecords())
                    .successRecords(listener.getSuccessRecords())
                    .errorRecords(listener.getErrorRecords())
                    // .errors(listener.getErrors()) // 暂时注释，类型不匹配
                    .endTime(endTime)
                    .processingTimeMs(processingTime)
                    .fileInfo(createFileInfo(file, targetSheetName))
                    .templateInfo(createTemplateInfo(template))
                    .build();

            return result;

        } catch (Exception e) {
            log.error("执行Excel导入失败", e);
            throw new IOException("执行Excel导入失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证导入参数
     */
    private void validateImportParameters(MultipartFile file, Integer templateId) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        if (templateId == null) {
            throw new IllegalArgumentException("模板ID不能为空");
        }

        validateFile(file);
    }

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        // 检查文件格式
        if (!excelConfig.isSupportedFormat(file.getOriginalFilename())) {
            throw new IllegalArgumentException("不支持的文件格式，支持的格式: " + excelConfig.getSupportedFormats());
        }

        // 检查文件大小
        if (!excelConfig.isFileSizeValid(file.getSize())) {
            throw new IllegalArgumentException("文件大小超过限制，最大允许: " + excelConfig.getMaxFileSize() + "MB");
        }
    }

    /**
     * 获取并验证模板
     */
    private GisManageTemplate getAndValidateTemplate(Integer templateId) {
        GisManageTemplate template = templateService.getTemplateById(templateId);
        if (template == null) {
            throw new IllegalArgumentException("模板不存在，模板ID: " + templateId);
        }

        // 验证模板是否支持Excel
        if (!template.isExcelTemplate() && !"excel".equalsIgnoreCase(template.getTemplateType())) {
            log.warn("模板类型不是Excel，但仍然尝试处理，模板ID: {}, 类型: {}", templateId, template.getTemplateType());
        }

        return template;
    }

    /**
     * 创建导入任务
     */
    private GisImportTask createImportTask(MultipartFile file, GisManageTemplate template, String createdBy) {
        if (createdBy == null) {
            return null; // 验证模式可能不需要创建任务
        }

        return taskService.createImportTask(
                "Excel导入-" + file.getOriginalFilename(),
                template.getId(),
                GisImportTask.ImportFormat.EXCEL,
                file.getOriginalFilename(),
                file.getSize(),
                createdBy
        );
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(GisImportTask task, ExcelImportResult result) {
        if (task == null) {
            return;
        }

        GisImportTask.DataStatus status = result.isSuccess() ?
                GisImportTask.DataStatus.DATA_IMPORTED :
                GisImportTask.DataStatus.NOT_IMPORTED;

        taskService.updateTaskStatus(task.getId(), status);
        // 更新任务统计信息（如果服务支持的话）
        try {
            // 这里可以调用更新统计的方法，如果GisImportTaskService有相关方法
            log.debug("更新任务统计 - 任务ID: {}, 成功: {}, 错误: {}",
                    task.getId(), result.getSuccessRecords(), result.getErrorRecords());
        } catch (Exception e) {
            log.warn("更新任务统计失败", e);
        }
    }

    /**
     * 计算处理时间
     */
    private long calculateProcessingTime(LocalDateTime startTime) {
        return java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
    }

    /**
     * 创建文件信息
     */
    private ExcelImportResult.FileInfo createFileInfo(MultipartFile file, String sheetName) {
        return ExcelImportResult.FileInfo.builder()
                .fileName(file.getOriginalFilename())
                .fileSize(file.getSize())
                .sheetName(sheetName)
                .fileType(file.getContentType())
                .build();
    }

    /**
     * 创建模板信息
     */
    private ExcelImportResult.TemplateInfo createTemplateInfo(GisManageTemplate template) {
        String coordinateTransform = null;
        if (template.getIsZh() != null && template.getIsZh()) {
            coordinateTransform = template.getOriginalCoordinateSystem() + " -> " + template.getTargetCoordinateSystem();
        }

        return ExcelImportResult.TemplateInfo.builder()
                .templateId(template.getId())
                .templateName(template.getNameZh())
                .tableName(template.getTableName())
                .datasourceName(template.getDatasourceName())
                .coordinateTransform(coordinateTransform)
                .build();
    }

    /**
     * 分析Excel文件结构（用于调试）
     */
    private void analyzeExcelFileStructure(MultipartFile file) {
        try {
            log.info("=== 开始分析Excel文件结构 ===");
            log.info("文件名: {}", file.getOriginalFilename());
            log.info("文件大小: {} bytes", file.getSize());
            log.info("文件类型: {}", file.getContentType());

            // 获取工作表名称
            List<String> sheetNames = ExcelUtil.getSheetNames(file);
            log.info("工作表数量: {}", sheetNames.size());
            for (int i = 0; i < sheetNames.size(); i++) {
                log.info("  工作表{}: '{}'", i, sheetNames.get(i));
            }

            // 分析第一个工作表的结构
            if (!sheetNames.isEmpty()) {
                Map<String, Object> analysis = ExcelUtil.analyzeExcelStructure(file, 1);
                log.info("第一个工作表分析结果: {}", analysis);
            }

            log.info("=== Excel文件结构分析完成 ===");
        } catch (Exception e) {
            log.error("分析Excel文件结构失败: {}", e.getMessage(), e);
        }
    }

    // ==================== 任务驱动模式新增方法实现 ====================

    @Override
    public String saveUploadedFile(MultipartFile file, String taskId) throws IOException {
        try {
            log.info("保存上传文件 - 任务ID: {}, 文件名: {}", taskId, file.getOriginalFilename());

            // 创建文件保存目录
            String uploadDir = tempPath;
            java.io.File dir = new java.io.File(uploadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String fileName = "excel_" + taskId + "_" + System.currentTimeMillis() + extension;
            String filePath = uploadDir + java.io.File.separator + fileName;

            // 保存文件
            java.io.File targetFile = new java.io.File(filePath);
            file.transferTo(targetFile);

            log.info("文件保存成功 - 路径: {}", filePath);
            return filePath;

        } catch (Exception e) {
            log.error("保存上传文件失败 - 任务ID: {}", taskId, e);
            throw new IOException("文件保存失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ValidationResult validateExcelData(String filePath, GisManageTemplate template,
                                            String sheetName, String target, String createdBy) {
        try {
            log.info("开始Excel数据验证 - 文件: {}, 模板ID: {}, 目标: {}", filePath, template.getId(), target);

            // 设置数据源
            if (template.getDataBase() != null) {
                DynamicDataSourceManager.build().useDataSource(template.getDataBase());
            }

            // 创建验证结果收集器
            ValidationResult result = new ValidationResult();
            result.setTotalRecords(0);
            result.setValidRecords(0);
            result.setErrorRecords(0);
            result.setErrors(new ArrayList<>());

            // 创建Excel数据监听器
            ExcelDataListener listener = new ExcelDataListener(
                template,
                validationService,
                coordinateTransformService,
                batchInsertService,
                1000,  // 批次大小
                target
            );

            // 读取Excel文件
            java.io.File file = new java.io.File(filePath);
            if (!file.exists()) {
                throw new RuntimeException("文件不存在: " + filePath);
            }

            // 使用EasyExcel读取数据
            if (sheetName != null && !sheetName.trim().isEmpty()) {
                EasyExcel.read(file, listener).sheet(sheetName).doRead();
            } else {
                EasyExcel.read(file, listener).sheet(0).doRead();
            }

            // 获取验证结果
            result.setTotalRecords(listener.getTotalRecords());
            result.setValidRecords(listener.getSuccessRecords());
            result.setErrorRecords(listener.getErrorRecords());
            result.setErrors(listener.getErrors());
            result.setPassed(listener.getErrorRecords() == 0);

            if (result.getTotalRecords() > 0) {
                result.setErrorRate((double) result.getErrorRecords() / result.getTotalRecords() * 100);
            }

            log.info("Excel数据验证完成 - 总记录: {}, 有效: {}, 错误: {}, 通过: {}",
                    result.getTotalRecords(), result.getValidRecords(), result.getErrorRecords(), result.isPassed());

            return result;

        } catch (Exception e) {
            log.error("Excel数据验证失败 - 文件: {}", filePath, e);

            ValidationResult errorResult = new ValidationResult();
            errorResult.setTotalRecords(0);
            errorResult.setValidRecords(0);
            errorResult.setErrorRecords(1);
            errorResult.setPassed(false);
            errorResult.setErrors(new ArrayList<>());

            ValidationResult.ValidationError error = new ValidationResult.ValidationError();
            error.setRecordIndex(0);
            error.setFeatureId("SYSTEM_ERROR");
            error.setFieldName("SYSTEM");
            error.setErrorType(ValidationResult.ErrorType.BUSINESS_RULE_VIOLATION);
            error.setErrorMessage("验证过程异常: " + e.getMessage());
            error.setErrorLevel(ValidationResult.ErrorLevel.CRITICAL);
            errorResult.getErrors().add(error);

            return errorResult;
        }
    }
}
