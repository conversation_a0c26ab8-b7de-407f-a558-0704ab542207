package com.zjxy.gisimportservice.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * 本地Swagger配置
 * 避免与framework包中的SwaggerConfig冲突
 * 暂时禁用以解决依赖冲突问题
 */
@Configuration
// @EnableSwagger2  // 暂时禁用
public class LocalSwaggerConfig {

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.zjxy.gisimportservice.controller"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("GIS数据导入服务API")
                .description("统一的文件导入任务管理API，支持Excel、Shapefile等多种格式")
                .version("1.0.0")
                .contact(new Contact("GIS团队", "", "<EMAIL>"))
                .build();
    }
}
