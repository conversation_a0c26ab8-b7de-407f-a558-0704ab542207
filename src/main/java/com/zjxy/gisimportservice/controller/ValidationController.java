package com.zjxy.gisimportservice.controller;

import com.zjxy.gisimportservice.entity.ValidationResult;
import com.zjxy.gisimportservice.service.UnifiedValidationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 验证服务REST API控制器
 * 提供统一的数据验证HTTP接口
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/validation")
@Api(tags = "数据验证服务")
public class ValidationController {

    @Autowired
    private UnifiedValidationService validationService;

    /**
     * 单记录验证
     */
    @PostMapping("/record")
    @ApiOperation("单记录验证")
    public ResponseEntity<Map<String, Object>> validateRecord(
            @ApiParam("验证请求") @RequestBody ValidationRecordRequest request) {

        Map<String, Object> response = new HashMap<>();

        try {
            log.info("接收单记录验证请求 - 模板ID: {}", request.getTemplateId());

            UnifiedValidationService.ValidationOptions options = new UnifiedValidationService.ValidationOptions();
            if (request.getOptions() != null) {
                options.setStrictMode((Boolean) request.getOptions().getOrDefault("strictMode", false));
                options.setSkipWarnings((Boolean) request.getOptions().getOrDefault("skipWarnings", false));
                options.setMaxErrors((Integer) request.getOptions().getOrDefault("maxErrors", 1000));
            }

            ValidationResult result = validationService.validateRecord(request.getTemplateId(), request.getData(), options);

            response.put("success", result.isPassed());
            response.put("code", "200");
            response.put("message", result.isPassed() ? "验证通过" : "验证失败");
            response.put("timestamp", LocalDateTime.now());
            response.put("data", buildValidationData(result, request.getTemplateId()));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("单记录验证失败", e);
            response.put("success", false);
            response.put("code", "500");
            response.put("message", "验证服务异常: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 批量验证
     */
    @PostMapping("/batch")
    @ApiOperation("批量验证")
    public ResponseEntity<Map<String, Object>> validateBatch(
            @ApiParam("批量验证请求") @RequestBody ValidationBatchRequest request) {

        Map<String, Object> response = new HashMap<>();

        try {
            log.info("接收批量验证请求 - 模板ID: {}, 记录数: {}", request.getTemplateId(), request.getData().size());

            UnifiedValidationService.ValidationOptions options = new UnifiedValidationService.ValidationOptions();
            if (request.getOptions() != null) {
                options.setStrictMode((Boolean) request.getOptions().getOrDefault("strictMode", false));
                options.setBatchSize((Integer) request.getOptions().getOrDefault("batchSize", 1000));
                options.setParallelProcessing((Boolean) request.getOptions().getOrDefault("parallelProcessing", true));
            }

            ValidationResult result = validationService.validateBatch(request.getTemplateId(), request.getData(), options);

            response.put("success", result.isPassed());
            response.put("code", "200");
            response.put("message", result.isPassed() ? "验证通过" : "验证失败");
            response.put("timestamp", LocalDateTime.now());
            response.put("data", buildValidationData(result, request.getTemplateId()));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("批量验证失败", e);
            response.put("success", false);
            response.put("code", "500");
            response.put("message", "验证服务异常: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 异步验证
     */
    @PostMapping("/async")
    @ApiOperation("异步验证")
    public ResponseEntity<Map<String, Object>> validateAsync(
            @ApiParam("异步验证请求") @RequestBody ValidationBatchRequest request) {

        Map<String, Object> response = new HashMap<>();

        try {
            log.info("接收异步验证请求 - 模板ID: {}, 记录数: {}", request.getTemplateId(), request.getData().size());

            UnifiedValidationService.ValidationOptions options = new UnifiedValidationService.ValidationOptions();
            options.setAsync(true);
            if (request.getOptions() != null) {
                options.setBatchSize((Integer) request.getOptions().getOrDefault("batchSize", 1000));
                options.setParallelProcessing((Boolean) request.getOptions().getOrDefault("parallelProcessing", true));
            }

            CompletableFuture<ValidationResult> futureResult = validationService.validateAsync(
                request.getTemplateId(), request.getData(), options);

            String validationId = "async_" + System.currentTimeMillis();

            response.put("success", true);
            response.put("code", "202");
            response.put("message", "异步验证已启动");
            response.put("timestamp", LocalDateTime.now());
            Map<String, Object> asyncData = new HashMap<>();
            asyncData.put("validationId", validationId);
            asyncData.put("status", "PROCESSING");
            asyncData.put("estimatedTime", "预计" + (request.getData().size() / 1000) + "秒");
            response.put("data", asyncData);

            return ResponseEntity.accepted().body(response);

        } catch (Exception e) {
            log.error("异步验证启动失败", e);
            response.put("success", false);
            response.put("code", "500");
            response.put("message", "异步验证启动失败: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取支持的验证类型
     */
    @GetMapping("/types")
    @ApiOperation("获取支持的验证类型")
    public ResponseEntity<Map<String, Object>> getSupportedTypes() {
        Map<String, Object> response = new HashMap<>();

        try {
            List<String> types = validationService.getSupportedValidationTypes();

            response.put("success", true);
            response.put("code", "200");
            response.put("message", "获取成功");
            response.put("timestamp", LocalDateTime.now());
            Map<String, Object> typesData = new HashMap<>();
            typesData.put("supportedTypes", types);
            response.put("data", typesData);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取验证类型失败", e);
            response.put("success", false);
            response.put("code", "500");
            response.put("message", "获取验证类型失败: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取验证规则
     */
    @GetMapping("/rules/{templateId}")
    @ApiOperation("获取验证规则")
    public ResponseEntity<Map<String, Object>> getValidationRules(
            @ApiParam("模板ID") @PathVariable String templateId) {

        Map<String, Object> response = new HashMap<>();

        try {
            List<Object> rules = validationService.getValidationRules(templateId);

            response.put("success", true);
            response.put("code", "200");
            response.put("message", "获取成功");
            response.put("timestamp", LocalDateTime.now());
            Map<String, Object> rulesData = new HashMap<>();
            rulesData.put("rules", rules);
            response.put("data", rulesData);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取验证规则失败", e);
            response.put("success", false);
            response.put("code", "500");
            response.put("message", "获取验证规则失败: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 构建验证数据响应
     */
    private Map<String, Object> buildValidationData(ValidationResult result, String templateId) {
        Map<String, Object> data = new HashMap<>();

        data.put("validationId", "val_" + System.currentTimeMillis());
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalRecords", result.getTotalRecords());
        summary.put("validRecords", result.getValidRecords());
        summary.put("errorRecords", result.getErrorRecords());
        summary.put("errorRate", result.getErrorRate());
        summary.put("passed", result.isPassed());
        data.put("summary", summary);

        data.put("errors", result.getErrors());

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("templateId", templateId);
        metadata.put("validationTime", LocalDateTime.now());
        metadata.put("validatorVersion", "1.0.0");
        data.put("metadata", metadata);

        return data;
    }

    /**
     * 单记录验证请求类
     */
    @Setter
    @Getter
    public static class ValidationRecordRequest {
        // Getters and Setters
        private String templateId;
        private Map<String, Object> data;
        private Map<String, Object> options;

    }

    /**
     * 批量验证请求类
     */
    @Setter
    @Getter
    public static class ValidationBatchRequest {
        // Getters and Setters
        private String templateId;
        private List<Map<String, Object>> data;
        private Map<String, Object> options;

    }
}
