package com.zjxy.gisimportservice.controller;

import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.framework.response.Result;
import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.ValidationResult;
import com.zjxy.gisimportservice.service.GisImportTaskService;
import com.zjxy.gisimportservice.service.UnifiedValidationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 任务驱动的Excel导入控制器
 * 基于gisresourcemanage项目架构设计
 */
@Slf4j
@RestController
@RequestMapping("/api/excel-import")
@Api(tags = "Excel导入任务管理")
public class ExcelImportTaskController {

    @Autowired
    private GisImportTaskService gisImportTaskService;

    @Autowired
    private UnifiedValidationService validationService;

    /**
     * 创建Excel导入任务
     */
    @PostMapping("/tasks")
    @ApiOperation("创建Excel导入任务")
    public Result<Map<String, Object>> createTask(
            @ApiParam("Excel文件") @RequestParam("file") MultipartFile file,
            @ApiParam("模板ID") @RequestParam("templateId") Integer templateId,
            @ApiParam("工作表名称") @RequestParam(value = "sheetName", required = false) String sheetName,
            @ApiParam("创建用户") @RequestParam("createdBy") String createdBy) {

        try {
            log.info("创建Excel导入任务 - 文件: {}, 模板ID: {}, 用户: {}", file.getOriginalFilename(), templateId, createdBy);

            // 1. 文件验证
            if (file.isEmpty()) {
                return Result.FAILURE("文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.toLowerCase().endsWith(".xlsx") && !fileName.toLowerCase().endsWith(".xls"))) {
                return Result.FAILURE("文件格式不支持，请上传Excel文件(.xlsx或.xls)");
            }

            if (file.getSize() > 100 * 1024 * 1024) { // 100MB
                return Result.FAILURE("文件大小不能超过100MB");
            }

            // 2. 创建任务
            GisImportTask task = new GisImportTask();
            task.setTaskId(UUID.randomUUID().toString());
            task.setTaskName("Excel导入-" + fileName);
            task.setTemplateId(templateId);
            task.setFileName(fileName);
            task.setFileSize(file.getSize());
            task.setSheetName(sheetName);
            task.setStatus("CREATED");
            task.setCreatedBy(createdBy);
            task.setDataType("excel");

            // 3. 保存文件并创建任务
            GisImportTask createdTask = gisImportTaskService.createExcelImportTask(task, file);

            // 4. 返回任务信息
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", createdTask.getTaskId());
            result.put("taskName", createdTask.getTaskName());
            result.put("fileName", createdTask.getFileName());
            result.put("fileSize", createdTask.getFileSize());
            result.put("status", createdTask.getStatus());
            result.put("createdTime", createdTask.getCreatedTime());

            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("创建Excel导入任务失败", e);
            return Result.FAILURE("创建任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务状态
     */
    @GetMapping("/tasks/{taskId}")
    @ApiOperation("查询任务状态")
    public Result<GisImportTask> getTaskStatus(@ApiParam("任务ID") @PathVariable String taskId) {
        try {
            log.info("查询任务状态 - 任务ID: {}", taskId);

            GisImportTask task = gisImportTaskService.getByTaskId(taskId);
            if (task == null) {
                return Result.FAILURE("任务不存在");
            }

            return Result.SUCCESS(task);

        } catch (Exception e) {
            log.error("查询任务状态失败", e);
            return Result.FAILURE("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务列表
     */
    @GetMapping("/tasks")
    @ApiOperation("查询任务列表")
    public Result<List<GisImportTask>> getTaskList(
            @ApiParam("创建用户") @RequestParam(value = "createdBy", required = false) String createdBy,
            @ApiParam("状态") @RequestParam(value = "status", required = false) String status,
            @ApiParam("页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @ApiParam("页大小") @RequestParam(value = "size", defaultValue = "10") Integer size) {

        try {
            log.info("查询任务列表 - 用户: {}, 状态: {}, 页码: {}, 页大小: {}", createdBy, status, page, size);

            List<GisImportTask> tasks = gisImportTaskService.getTaskList(createdBy, status, "excel", page, size);
            return Result.SUCCESS(tasks);

        } catch (Exception e) {
            log.error("查询任务列表失败", e);
            return Result.FAILURE("查询失败: " + e.getMessage());
        }
    }

    /**
     * Excel数据验证接口
     */
    @PostMapping("/tasks/{taskId}/validate")
    @ApiOperation("Excel数据验证")
    public Result<ValidationResult> validateData(@ApiParam("任务ID") @PathVariable String taskId) {
        try {
            log.info("开始Excel数据验证 - 任务ID: {}", taskId);

            // 1. 获取任务信息
            GisImportTask task = gisImportTaskService.getByTaskId(taskId);
            if (task == null) {
                return Result.FAILURE("任务不存在");
            }

            if (!"CREATED".equals(task.getStatus())) {
                return Result.FAILURE("任务状态不正确，当前状态: " + task.getStatus());
            }

            // 2. 更新任务状态为验证中
            gisImportTaskService.updateTaskStatus(taskId, "VALIDATING", null);

            // 3. 设置数据源
            if (task.getDatasourceName() != null) {
                DynamicDataSourceManager.build().useDataSource(task.getDatasourceName());
            }

            // 4. 执行验证
            ValidationResult validationResult = gisImportTaskService.validateExcelData(task);

            // 5. 更新任务状态和验证结果
            String newStatus = validationResult.isPassed() ? "VALIDATED" : "FAILED";
            gisImportTaskService.updateTaskStatus(taskId, newStatus, validationResult);

            return Result.SUCCESS(validationResult);

        } catch (Exception e) {
            log.error("Excel数据验证失败", e);
            // 更新任务状态为失败
            try {
                gisImportTaskService.updateTaskStatus(taskId, "FAILED", null);
            } catch (Exception ex) {
                log.error("更新任务状态失败", ex);
            }
            return Result.FAILURE("验证失败: " + e.getMessage());
        }
    }

    /**
     * Excel数据导入执行接口
     */
    @PostMapping("/tasks/{taskId}/execute")
    @ApiOperation("Excel数据导入执行")
    public Result<Map<String, Object>> executeImport(@ApiParam("任务ID") @PathVariable String taskId) {
        try {
            log.info("开始Excel数据导入 - 任务ID: {}", taskId);

            // 1. 获取任务信息
            GisImportTask task = gisImportTaskService.getByTaskId(taskId);
            if (task == null) {
                return Result.FAILURE("任务不存在");
            }

            if (!"VALIDATED".equals(task.getStatus())) {
                return Result.FAILURE("任务状态不正确，请先执行数据验证。当前状态: " + task.getStatus());
            }

            // 2. 更新任务状态为导入中
            gisImportTaskService.updateTaskStatus(taskId, "IMPORTING", null);

            // 3. 设置数据源
            if (task.getDatasourceName() != null) {
                DynamicDataSourceManager.build().useDataSource(task.getDatasourceName());
            }

            // 4. 执行导入
            Map<String, Object> importResult = gisImportTaskService.executeExcelImport(task);

            // 5. 更新任务状态为完成
            gisImportTaskService.updateTaskStatus(taskId, "COMPLETED", null);

            return Result.SUCCESS(importResult);

        } catch (Exception e) {
            log.error("Excel数据导入失败", e);
            // 更新任务状态为失败
            try {
                gisImportTaskService.updateTaskStatus(taskId, "FAILED", null);
            } catch (Exception ex) {
                log.error("更新任务状态失败", ex);
            }
            return Result.FAILURE("导入失败: " + e.getMessage());
        }
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/tasks/{taskId}")
    @ApiOperation("删除任务")
    public Result<Void> deleteTask(@ApiParam("任务ID") @PathVariable String taskId) {
        try {
            log.info("删除任务 - 任务ID: {}", taskId);

            boolean deleted = gisImportTaskService.deleteTask(taskId);
            if (deleted) {
                return Result.SUCCESS();
            } else {
                return Result.FAILURE("任务不存在或删除失败");
            }

        } catch (Exception e) {
            log.error("删除任务失败", e);
            return Result.FAILURE("删除失败: " + e.getMessage());
        }
    }
}
